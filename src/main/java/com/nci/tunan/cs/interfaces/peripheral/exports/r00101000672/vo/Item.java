package com.nci.tunan.cs.interfaces.peripheral.exports.r00101000672.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class Item implements Serializable{
	private String RiskCode;//险种代码
	
	private String RiskName;//险种名称
	
	private String MoneyName;//费用名称
	
	private String SumMoney;//费用金额

	@XmlElement(name = "RiskCode")
	public String getRiskCode() {
		return RiskCode;
	}

	public void setRiskCode(String riskCode) {
		RiskCode = riskCode;
	}

	@XmlElement(name = "RiskName")
	public String getRiskName() {
		return RiskName;
	}

	public void setRiskName(String riskName) {
		RiskName = riskName;
	}

	@XmlElement(name = "MoneyName")
	public String getMoneyName() {
		return MoneyName;
	}

	public void setMoneyName(String moneyName) {
		MoneyName = moneyName;
	}

	@XmlElement(name = "SumMoney")
	public String getSumMoney() {
		return SumMoney;
	}

	public void setSumMoney(String sumMoney) {
		SumMoney = sumMoney;
	}
	
	
}
