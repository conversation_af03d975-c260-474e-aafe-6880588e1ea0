package com.nci.tunan.cs.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.cs.dao.ICsDocPrFileInfoDao;
import com.nci.tunan.cs.model.po.CsDocPrFileInfoPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * @description CsDocPrFileInfoDaoImpl实现类
 * <AUTHOR>
 * @date 2022-05-10 10:55:38
 */
public class CsDocPrFileInfoDaoImpl extends BaseDaoImpl implements ICsDocPrFileInfoDao {
	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * @description 增加数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPO
	 *            对象
	 * @return CsDocPrFileInfoPO 添加结果
	 */
	public CsDocPrFileInfoPO addCsDocPrFileInfo(CsDocPrFileInfoPO csDocPrFileInfoPO) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--addCsDocPrFileInfo======>");
		return createObject("CS_addCsDocPrFileInfo", csDocPrFileInfoPO);
	}

	/**
	 * @description 删除数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPO
	 *            对象
	 * @return boolean 删除是否成功
	 */
	public boolean deleteCsDocPrFileInfo(CsDocPrFileInfoPO csDocPrFileInfoPO) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--deleteCsDocPrFileInfo======>");
		return deleteObject("deleteCsDocPrFileInfo", csDocPrFileInfoPO);
	}

	/**
	 * @description 修改数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPO
	 *            对象
	 * @return CsDocPrFileInfoPO 修改结果
	 */
	public CsDocPrFileInfoPO updateCsDocPrFileInfo(CsDocPrFileInfoPO csDocPrFileInfoPO) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--updateCsDocPrFileInfo======>");
		return updateObject("updateCsDocPrFileInfo", csDocPrFileInfoPO);
	}

	/**
	 * @description 查询单条数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPO
	 *            对象
	 * @return CsDocPrFileInfoPO 查询结果对象
	 */
	public CsDocPrFileInfoPO findCsDocPrFileInfo(CsDocPrFileInfoPO csDocPrFileInfoPO) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--findCsDocPrFileInfo======>");
		return findObject("findCsDocPrFileInfo", csDocPrFileInfoPO);
	}

	/**
	 * @description 查询所有数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPO
	 *            对象
	 * @return List<CsDocPrFileInfoPO> 查询结果List
	 */
	public List<CsDocPrFileInfoPO> findAllCsDocPrFileInfo(CsDocPrFileInfoPO csDocPrFileInfoPO) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--findAllCsDocPrFileInfo======>");
		return findAll("findAllCsDocPrFileInfo", csDocPrFileInfoPO);
	}

	/**
	 * @description 查询数据条数
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPO
	 *            对象
	 * @return int 查询结果条数
	 */
	public int findCsDocPrFileInfoTotal(CsDocPrFileInfoPO csDocPrFileInfoPO) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--findCsDocPrFileInfoTotal======>");
		return findCount("findCsDocPrFileInfoTotal", csDocPrFileInfoPO);
	}

	/**
	 * @description 分页查询数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param currentPage
	 *            当前页对象
	 * @return CurrentPage<CsDocPrFileInfoPO> 查询结果的当前页对象
	 */
	public CurrentPage<CsDocPrFileInfoPO> queryCsDocPrFileInfoForPage(CsDocPrFileInfoPO csDocPrFileInfoPO,
			CurrentPage<CsDocPrFileInfoPO> currentPage) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--queryCsDocPrFileInfoForPage======>");
		currentPage.setParamObject(csDocPrFileInfoPO);
		return queryForPage("findCsDocPrFileInfoTotal", "queryCsDocPrFileInfoForPage", currentPage);
	}

	/**
	 * @description 批量增加数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPOList
	 *            对象列表
	 * @return boolean 批量添加是否成功
	 */
	public boolean batchSaveCsDocPrFileInfo(List<CsDocPrFileInfoPO> csDocPrFileInfoPOList) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--batchSaveCsDocPrFileInfo======>");
		return batchSave("addCsDocPrFileInfo", csDocPrFileInfoPOList);
	}

	/**
	 * @description 批量修改数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPOList
	 *            对象列表
	 * @return boolean 批量修改是否成功
	 */
	public boolean batchUpdateCsDocPrFileInfo(List<CsDocPrFileInfoPO> csDocPrFileInfoPOList) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--batchUpdateCsDocPrFileInfo======>");
		return batchUpdate("updateCsDocPrFileInfo", csDocPrFileInfoPOList);
	}

	/**
	 * @description 批量删除数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPOList
	 *            对象列表
	 * @return boolean 批量删除是否成功
	 */
	public boolean batchDeleteCsDocPrFileInfo(List<CsDocPrFileInfoPO> csDocPrFileInfoPOList) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--batchDeleteCsDocPrFileInfo======>");
		return batchDelete("deleteCsDocPrFileInfo", csDocPrFileInfoPOList);
	}

	/**
	 * @description 查询所有数据 ，重新组装为map
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csDocPrFileInfoPO
	 *            对象
	 * @return List<Map<String, Object>> 查询结果存放到map中
	 */
	public List<Map<String, Object>> findAllMapCsDocPrFileInfo(CsDocPrFileInfoPO csDocPrFileInfoPO) {
		logger.debug("<======CsDocPrFileInfoDaoImpl--findAllMapCsDocPrFileInfo======>");
		return findAllMap("findAllMapCsDocPrFileInfo", csDocPrFileInfoPO);
	}
}
