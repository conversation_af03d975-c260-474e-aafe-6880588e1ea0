package com.nci.tunan.cs.dao.impl;

import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import com.nci.tunan.cs.interfaces.model.po.CsEncryptionCfgPO;
import org.slf4j.Logger;

import java.util.List;

import com.nci.tunan.cs.dao.ICsEncryptionCfgDao;
import com.nci.udmp.util.logging.LoggerFactory;


/**
 * @description 保全加密信息配置表Dao层
 * <AUTHOR> <EMAIL>
 * @date 2024-11-21 17:29:21
 * @.belongToModule 保全子系统
 **/
public class CsEncryptionCfgDaoImpl extends BaseDaoImpl implements ICsEncryptionCfgDao {
	/**
	 * @Fields logger :  日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * @description 增加数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPO 对象
	 * @return CsEncryptionCfgPO 添加结果
	 */
	public CsEncryptionCfgPO addCsEncryptionCfg(CsEncryptionCfgPO csEncryptionCfgPO) {
		logger.debug("<======CsEncryptionCfgDaoImpl--CUS_addCsEncryptionCfg======>");
		return createObject("CUS_addCsEncryptionCfg", csEncryptionCfgPO);
	}

	/**
	 * @description 删除数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPO 对象
	 * @return boolean 删除是否成功
	 */
	public boolean deleteCsEncryptionCfg(CsEncryptionCfgPO csEncryptionCfgPO) {
		logger.debug("<======CsEncryptionCfgDaoImpl--CUS_deleteCsEncryptionCfg======>");
		return deleteObject("CUS_deleteCsEncryptionCfg", csEncryptionCfgPO);
	}

	/**
	 * @description 修改数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPO 对象
	 * @return CsEncryptionCfgPO 修改结果
	 */
	public CsEncryptionCfgPO updateCsEncryptionCfg(CsEncryptionCfgPO csEncryptionCfgPO) {
		logger.debug("<======CsEncryptionCfgDaoImpl--CUS_updateCsEncryptionCfg======>");
		return updateObject("CUS_updateCsEncryptionCfg", csEncryptionCfgPO);
	}

	/**
	 * @description 查询单条数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPO 对象
	 * @return CsEncryptionCfgPO 查询结果对象
	 */
	public CsEncryptionCfgPO findCsEncryptionCfg(CsEncryptionCfgPO csEncryptionCfgPO) {
		logger.debug("<======CsEncryptionCfgDaoImpl--CUS_findCsEncryptionCfg======>");
		return findObject("CUS_findCsEncryptionCfg", csEncryptionCfgPO);
	}

	/**
	 * @description 查询所有数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPO 对象
	 * @return List<CsEncryptionCfgPO> 查询结果List
	 */
	public List<CsEncryptionCfgPO> findAllCsEncryptionCfg(CsEncryptionCfgPO csEncryptionCfgPO) {
		logger.debug("<======CsEncryptionCfgDaoImpl--CUS_findAllCsEncryptionCfg======>");
		return findAll("CUS_findAllCsEncryptionCfg", csEncryptionCfgPO);
	}

	/**
	 * @description 查询数据条数
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPO 对象
	 * @return int 查询结果条数
	 */
	public int findCsEncryptionCfgTotal(CsEncryptionCfgPO csEncryptionCfgPO) {
		logger.debug("<======CsEncryptionCfgDaoImpl--CUS_findCsEncryptionCfgTotal======>");
		return findCount("CUS_findCsEncryptionCfgTotal", csEncryptionCfgPO);
	}

	/**
	 * @description 分页查询数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param currentPage 当前页对象
	 * @return CurrentPage<CsEncryptionCfgPO> 查询结果的当前页对象
	 */
	public CurrentPage<CsEncryptionCfgPO> queryCsEncryptionCfgForPage(CsEncryptionCfgPO csEncryptionCfgPO, CurrentPage<CsEncryptionCfgPO> currentPage) {
		logger.debug("<======CsEncryptionCfgDaoImpl--CUS_queryCsEncryptionCfgForPage======>");
		currentPage.setParamObject(csEncryptionCfgPO);
		return queryForPage("CUS_findCsEncryptionCfgTotal", "CUS_queryCsEncryptionCfgForPage", currentPage);
	}

	/**
	 * @description 批量增加数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPOList 对象列表
	 * @return boolean 批量添加是否成功
	 */
	public boolean batchSaveCsEncryptionCfg(List<CsEncryptionCfgPO> csEncryptionCfgPOList) {
		logger.debug("<======CsEncryptionCfgDaoImpl--batchSaveCsEncryptionCfg======>");
		return batchSave("CUS_addCsEncryptionCfg", csEncryptionCfgPOList);
	}

	/**
	 * @description 批量修改数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPOList 对象列表
	 * @return boolean 批量修改是否成功
	 */
	public boolean batchUpdateCsEncryptionCfg(List<CsEncryptionCfgPO> csEncryptionCfgPOList) {
		logger.debug("<======CsEncryptionCfgDaoImpl--batchUpdateCsEncryptionCfg======>");
		return batchUpdate("CUS_updateCsEncryptionCfg", csEncryptionCfgPOList);
	}

	/**
	 * @description 批量删除数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPOList 对象列表
	 * @return boolean 批量删除是否成功
	 */
	public boolean batchDeleteCsEncryptionCfg(List<CsEncryptionCfgPO> csEncryptionCfgPOList) {
		logger.debug("<======CsEncryptionCfgDaoImpl--batchDeleteCsEncryptionCfg======>");
		return batchDelete("CUS_deleteCsEncryptionCfg", csEncryptionCfgPOList);
	}

	/**
	 * @description 查询所有数据 ，重新组装为map
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csEncryptionCfgPO 对象
	 * @return List<Map < String, Object>> 查询结果存放到map中
	 */
	public List<Map<String, Object>> findAllMapCsEncryptionCfg(CsEncryptionCfgPO csEncryptionCfgPO) {
		logger.debug("<======CsEncryptionCfgDaoImpl--CUS_findAllMapCsEncryptionCfg======>");
		return findAllMap("CUS_findAllMapCsEncryptionCfg", csEncryptionCfgPO);
	}
}
