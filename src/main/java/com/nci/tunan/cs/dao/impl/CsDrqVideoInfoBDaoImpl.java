package com.nci.tunan.cs.dao.impl;

import com.nci.tunan.cs.dao.ICsDrqVideoInfoBDao;
import com.nci.tunan.cs.model.po.CsDrqVideoInfoBPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description CsDrqVideoInfoBDaoImpl实现类
 * <AUTHOR> 
 * @date 2021-06-03 20:03:16  
 */
public class CsDrqVideoInfoBDaoImpl  extends BaseDaoImpl  implements ICsDrqVideoInfoBDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPO 对象
     * @return CsDrqVideoInfoBPO 添加结果
     */
	 public CsDrqVideoInfoBPO addCsDrqVideoInfoB(CsDrqVideoInfoBPO csDrqVideoInfoBPO) {
	 	 logger.debug("<======CsDrqVideoInfoBDaoImpl--addCsDrqVideoInfoB======>");
		 return createObject("CUS_addCsDrqVideoInfoB",  csDrqVideoInfoBPO) ;
	 }
    
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPO 对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteCsDrqVideoInfoB(CsDrqVideoInfoBPO csDrqVideoInfoBPO) {
 	 	 logger.debug("<======CsDrqVideoInfoBDaoImpl--deleteCsDrqVideoInfoB======>");
		 return deleteObject("deleteCsDrqVideoInfoB",  csDrqVideoInfoBPO) ;
	 }

	/**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPO 对象
     * @return CsDrqVideoInfoBPO 修改结果
     */
 	 public CsDrqVideoInfoBPO updateCsDrqVideoInfoB(CsDrqVideoInfoBPO csDrqVideoInfoBPO) {
 	 	 logger.debug("<======CsDrqVideoInfoBDaoImpl--updateCsDrqVideoInfoB======>");
		 return updateObject("CUS_updateCsDrqVideoInfoB",  csDrqVideoInfoBPO) ;
 	 }	

    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPO 对象
     * @return CsDrqVideoInfoBPO 查询结果对象
     */
	 public CsDrqVideoInfoBPO findCsDrqVideoInfoB(CsDrqVideoInfoBPO csDrqVideoInfoBPO) {
	  	logger.debug("<======CsDrqVideoInfoBDaoImpl--findCsDrqVideoInfoB======>");
		return findObject("findCsDrqVideoInfoB",  csDrqVideoInfoBPO) ;
	 }
	
	 /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPO 对象
     * @return List<CsDrqVideoInfoBPO> 查询结果List
     */
	 public List<CsDrqVideoInfoBPO> findAllCsDrqVideoInfoB(CsDrqVideoInfoBPO csDrqVideoInfoBPO) {
	 	logger.debug("<======CsDrqVideoInfoBDaoImpl--findAllCsDrqVideoInfoB======>");
		return findAll("CUS_findAllCsDrqVideoInfoB",  csDrqVideoInfoBPO) ;
	 }
	
	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPO 对象
     * @return int 查询结果条数
     */
	 public int findCsDrqVideoInfoBTotal(CsDrqVideoInfoBPO csDrqVideoInfoBPO) {
	 	logger.debug("<======CsDrqVideoInfoBDaoImpl--findCsDrqVideoInfoBTotal======>");
		return findCount("findCsDrqVideoInfoBTotal",  csDrqVideoInfoBPO) ;
	 }
	
	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CsDrqVideoInfoBPO> 查询结果的当前页对象
     */
	 public CurrentPage<CsDrqVideoInfoBPO> queryCsDrqVideoInfoBForPage(CsDrqVideoInfoBPO csDrqVideoInfoBPO, CurrentPage<CsDrqVideoInfoBPO> currentPage) {
		logger.debug("<======CsDrqVideoInfoBDaoImpl--queryCsDrqVideoInfoBForPage======>");
		currentPage.setParamObject(csDrqVideoInfoBPO);
		return queryForPage("findCsDrqVideoInfoBTotal", "queryCsDrqVideoInfoBForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCsDrqVideoInfoB(List<CsDrqVideoInfoBPO> csDrqVideoInfoBPOList) {
	 	logger.debug("<======CsDrqVideoInfoBDaoImpl--batchSaveCsDrqVideoInfoB======>");
		return batchSave("addCsDrqVideoInfoB", csDrqVideoInfoBPOList) ;
	 }
	
	 /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCsDrqVideoInfoB(List<CsDrqVideoInfoBPO> csDrqVideoInfoBPOList) {
	 	logger.debug("<======CsDrqVideoInfoBDaoImpl--batchUpdateCsDrqVideoInfoB======>");
		return batchUpdate("updateCsDrqVideoInfoB", csDrqVideoInfoBPOList) ;
	 }
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCsDrqVideoInfoB(List<CsDrqVideoInfoBPO> csDrqVideoInfoBPOList) {
	 	logger.debug("<======CsDrqVideoInfoBDaoImpl--batchDeleteCsDrqVideoInfoB======>");
		return batchDelete("deleteCsDrqVideoInfoB", csDrqVideoInfoBPOList) ;
	 }
	
	 /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param csDrqVideoInfoBPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapCsDrqVideoInfoB(CsDrqVideoInfoBPO csDrqVideoInfoBPO) {
	 	logger.debug("<======CsDrqVideoInfoBDaoImpl--findAllMapCsDrqVideoInfoB======>");
		return findAllMap("findAllMapCsDrqVideoInfoB", csDrqVideoInfoBPO) ;
	 }
}
