package com.nci.tunan.cs.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.core.common.interfaces.model.po.AgentLicensePO;
import com.nci.tunan.cs.dao.ICsContractAgentDao;
import com.nci.tunan.cs.model.po.CsContractAgentPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;


/**
 * 
 * @description 保单与代理人关系表记录保单所有的代理人DaoImpl
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @.belongToModule cs-保全子系统-保单与代理人关系表 
 * @date 2020年12月23日  上午10:20:57
 */
public class CsContractAgentDaoImpl  extends BaseDaoImpl  implements ICsContractAgentDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 通过保单与代理人关系PO对象增加保单与代理人关系数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPO 保单与代理人关系表记录保单所有的代理人PO对象
     * @return CsContractAgentPO 添加结果
     */
	 public CsContractAgentPO addCsContractAgent(CsContractAgentPO csContractAgentPO) {
	 	 logger.debug("<======CsContractAgentDaoImpl--addCsContractAgent======>");
		 return createObject("addCsContractAgent",  csContractAgentPO) ;
	 }
    
     /**
     * @description 通过保单与代理人关系PO对象删除保单与代理人关系数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPO 保单与代理人关系表记录保单所有的代理人PO
     * @return boolean 删除是否成功
     */
 	 public boolean deleteCsContractAgent(CsContractAgentPO csContractAgentPO) {
 	 	 logger.debug("<======CsContractAgentDaoImpl--deleteCsContractAgent======>");
		 return deleteObject("deleteCsContractAgent",  csContractAgentPO) ;
	 }

	/**
     * @description 通过保单与代理人关系PO对象修改保单与代理人关系数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPO  保单与代理人关系表记录保单所有的代理人PO
     * @return CsContractAgentPO 修改结果
     */
 	 public CsContractAgentPO updateCsContractAgent(CsContractAgentPO csContractAgentPO) {
 	 	 logger.debug("<======CsContractAgentDaoImpl--updateCsContractAgent======>");
		 return updateObject("updateCsContractAgent",  csContractAgentPO) ;
 	 }	

    /**
     * @description 通过保单与代理人关系PO对象查询单条保单与代理人关系数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPO 保单与代理人关系表记录保单所有的代理人PO
     * @return CsContractAgentPO 查询结果对象
     */
	 public CsContractAgentPO findCsContractAgent(CsContractAgentPO csContractAgentPO) {
	  	logger.debug("<======CsContractAgentDaoImpl--findCsContractAgent======>");
		return findObject("findCsContractAgent",  csContractAgentPO) ;
	 }
	
	 /**
     * @description 通过保单与代理人关系PO对象查询所有保单与代理人关系数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPO  保单与代理人关系表记录保单所有的代理人PO
     * @return List<CsContractAgentPO> 查询结果List
     */
	 public List<CsContractAgentPO> findAllCsContractAgent(CsContractAgentPO csContractAgentPO) {
	 	logger.debug("<======CsContractAgentDaoImpl--findAllCsContractAgent======>");
		return findAll("findAllCsContractAgent",  csContractAgentPO) ;
	 }
	
	 /**
     * @description 通过保单与代理人关系PO对象查询所有保单与代理人关系数据条数
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPO 保单与代理人关系表记录保单所有的代理人PO
     * @return int 查询结果条数
     */
	 public int findCsContractAgentTotal(CsContractAgentPO csContractAgentPO) {
	 	logger.debug("<======CsContractAgentDaoImpl--findCsContractAgentTotal======>");
		return findCount("findCsContractAgentTotal",  csContractAgentPO) ;
	 }
	
	 /**
	  * 
	  * @description 通过保单与代理人关系PO对象分页查询保单与代理人关系数据
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param csContractAgentPO 保单与代理人关系PO对象
	  * @param currentPage  当前页对象
	  * @return CurrentPage<CsContractAgentPO> 查询结果的当前页对象
	  */
	 public CurrentPage<CsContractAgentPO> queryCsContractAgentForPage(CsContractAgentPO csContractAgentPO, CurrentPage<CsContractAgentPO> currentPage) {
		logger.debug("<======CsContractAgentDaoImpl--queryCsContractAgentForPage======>");
		currentPage.setParamObject(csContractAgentPO);
		return queryForPage("findCsContractAgentTotal", "queryCsContractAgentForPage",  currentPage) ;
	 }
	
	/**
     * @description 通过保单与代理人关系PO对象集合批量增加保单与代理人关系数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPOList 保单与代理人关系表记录保单所有的代理人PO
     * @return boolean 批量添加是否成功
     */
	
	 public boolean batchSaveCsContractAgent(List<CsContractAgentPO> csContractAgentPOList) {
	 	logger.debug("<======CsContractAgentDaoImpl--batchSaveCsContractAgent======>");
		return batchSave("addCsContractAgent", csContractAgentPOList) ;
	 }
	
	 /**
     * @description 通过保单与代理人关系PO对象集合批量修改保单与代理人关系数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPOList 保单与代理人关系表记录保单所有的代理人PO
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCsContractAgent(List<CsContractAgentPO> csContractAgentPOList) {
	 	logger.debug("<======CsContractAgentDaoImpl--batchUpdateCsContractAgent======>");
		return batchUpdate("updateCsContractAgent", csContractAgentPOList) ;
	 }
	
	/**
     * @description 通过保单与代理人关系PO对象集合批量删除保单与代理人关系数据
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPOList 保单与代理人关系表记录保单所有的代理人PO
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCsContractAgent(List<CsContractAgentPO> csContractAgentPOList) {
	 	logger.debug("<======CsContractAgentDaoImpl--batchDeleteCsContractAgent======>");
		return batchDelete("deleteCsContractAgent", csContractAgentPOList) ;
	 }
	
	 /**
     * @description 通过保单与代理人关系PO对象查询所有数据 ，重新组装为map
     * @version *******
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractAgentPO 保单与代理人关系表记录保单所有的代理人PO
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapCsContractAgent(CsContractAgentPO csContractAgentPO) {
	 	logger.debug("<======CsContractAgentDaoImpl--findAllMapCsContractAgent======>");
		return findAllMap("findAllMapCsContractAgent", csContractAgentPO) ;
	 }
	 
	 
     /** 
     * @description 根据代理人资格证PO条件查询保单与代理人关系表记录保单所有的代理人信息
     * @version ******* 
     * <AUTHOR> <EMAIL>
     * @param AgentLicensePO 代理人资格证PO
     * @return List<AgentLicensePO> 返回查询结果
     */
	 public List<AgentLicensePO> findCsContractAgentLicenses(AgentLicensePO agentLicensePO) {
		return findAll("findCsContractAgentLicense",  agentLicensePO) ;
	 }

	
	/**
	 * 
	 * @description 通过保单与代理人关系PO对象查询保单与代理人关系数据数量
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csContractAgentPO 保单与代理人关系PO对象
	 * @return int 查询数量
	 */
	public int findByAgentCodeAndPolicyCode(CsContractAgentPO csContractAgentPO) {
		return findCount("findAgentCodeAndPolicyCode",  csContractAgentPO) ;
	}
}
