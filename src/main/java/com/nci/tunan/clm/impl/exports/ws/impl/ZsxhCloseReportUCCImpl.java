package com.nci.tunan.clm.impl.exports.ws.impl;

import com.nci.tunan.clm.impl.exports.ws.IZsxhCloseReportUCC;
import com.nci.tunan.clm.impl.mobile.service.IZsxhCloseReportService;
import com.nci.tunan.clm.interfaces.vo.zsxhclosereport.CloseReportReqVO;
import com.nci.tunan.clm.interfaces.vo.zsxhclosereport.CloseReportResVO;

public class ZsxhCloseReportUCCImpl implements IZsxhCloseReportUCC {
    private IZsxhCloseReportService zsxhCloseReportService;
    
    @Override
    public CloseReportResVO closeReport(CloseReportReqVO closeReportReqVO) {
        CloseReportResVO closeReportResVO = zsxhCloseReportService.closeReport(closeReportReqVO);
        return closeReportResVO;
    }

    public IZsxhCloseReportService getZsxhCloseReportService() {
        return zsxhCloseReportService;
    }

    public void setZsxhCloseReportService(IZsxhCloseReportService zsxhCloseReportService) {
        this.zsxhCloseReportService = zsxhCloseReportService;
    }
}
