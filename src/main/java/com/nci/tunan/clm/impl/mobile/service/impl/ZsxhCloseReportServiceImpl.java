package com.nci.tunan.clm.impl.mobile.service.impl;

import com.nci.tunan.clm.impl.mobile.service.IZsxhCloseReportService;
import com.nci.tunan.clm.interfaces.vo.zsxhclosereport.CloseReportReqVO;
import com.nci.tunan.clm.interfaces.vo.zsxhclosereport.CloseReportResVO;

public class ZsxhCloseReportServiceImpl implements IZsxhCloseReportService {
    @Override
    public CloseReportResVO closeReport(CloseReportReqVO closeReportReqVO) {
        return null;
    }
}
