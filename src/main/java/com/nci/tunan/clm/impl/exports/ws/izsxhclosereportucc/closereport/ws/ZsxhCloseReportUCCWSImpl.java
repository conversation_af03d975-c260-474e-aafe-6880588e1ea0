package com.nci.tunan.clm.impl.exports.ws.izsxhclosereportucc.closereport.ws;

import com.nci.tunan.clm.impl.exports.ws.IZsxhCloseReportUCC;
import com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport.SrvReqBody;
import com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport.SrvResBody;
import com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport.SrvResBizBody;
import com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport.ws.IZsxhCloseReportUCCWS;
import com.nci.tunan.clm.interfaces.vo.zsxhclosereport.CloseReportReqVO;
import com.nci.tunan.clm.interfaces.vo.zsxhclosereport.CloseReportResVO;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.logging.LoggerFactory;
import org.slf4j.Logger;

import javax.jws.WebService;
import javax.xml.ws.Holder;

@WebService(endpointInterface = "com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport.ws.IZsxhCloseReportUCCWS", serviceName = "ZsxhCloseReportUCCWSImplcloseReport")
public class ZsxhCloseReportUCCWSImpl implements IZsxhCloseReportUCCWS {

    /**
     * @Fields logger : 日志工具 
     */
    private static Logger logger = LoggerFactory.getLogger();
    
    private IZsxhCloseReportUCC zsxhCloseReportUCC;

    @Override
    public void closeReport(SysHeader parametersReqHeader, SrvReqBody parametersReqBody, Holder<SysHeader> parametersResHeader, Holder<SrvResBody> parametersResBody) {
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
        CommonHeaderDeal.setBIZHEADERTHREAD_EXB(parametersReqBody.getBizHeader());
        CloseReportReqVO inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        try {
            CloseReportResVO output = zsxhCloseReportUCC.closeReport(inputVO);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
            BizHeaderExB bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
            SrvResBizBody bizResBody = new SrvResBizBody();
            bizResBody.setOutputData(output);
            srvResBody.setBizBody(bizResBody);
            srvResBody.setBizHeader(bizHeader);
            parametersResHeader.value = sysHeader;
            parametersResBody.value = srvResBody;
        } catch (Exception e2) {
            logger.error("调用接口过程中产生异常!",e2);
        }
        try {
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(parametersResHeader.value));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersResBody.value.getBizHeader()));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody.value.getBizBody()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
        }
    }

    public IZsxhCloseReportUCC getZsxhCloseReportUCC() {
        return zsxhCloseReportUCC;
    }

    public void setZsxhCloseReportUCC(IZsxhCloseReportUCC zsxhCloseReportUCC) {
        this.zsxhCloseReportUCC = zsxhCloseReportUCC;
    }
}
    
