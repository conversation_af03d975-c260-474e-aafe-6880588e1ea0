package com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport.ws;


import com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport.SrvReqBody;
import com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport.SrvResBody;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;

import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Holder;

@WebService
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface IZsxhCloseReportUCCWS {
    public void closeReport(
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, partName = "parametersReqHeader") SysHeader parametersReqHeader,
            @WebParam(name = "request", targetNamespace = "http://www.newchinalife.com/service/bd", partName = "parametersReqBody") SrvReqBody parametersReqBody,
            @WebParam(name = "sysHeader",
                    targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, mode = WebParam.Mode.OUT, partName = "parametersResHeader") Holder<SysHeader> parametersResHeader,
            @WebParam(name = "response", targetNamespace = "http://www.newchinalife.com/service/bd", mode = WebParam.Mode.OUT, partName = "parametersResBody") Holder<SrvResBody> parametersResBody
    );
}
