package com.nci.tunan.clm.interfaces.exports.hs.imodifyUwConditionucc.modifyUwCondition;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private final static  long serialVersionUID = 1L;
    
    @XmlElement(name = "OutputData")
    protected com.nci.tunan.clm.interfaces.vo.modifyUwCondition.ModifyUwConditionResVO outputData;

    public com.nci.tunan.clm.interfaces.vo.modifyUwCondition.ModifyUwConditionResVO getOutputData() {
        return outputData;
    }
    public void setOutputData(com.nci.tunan.clm.interfaces.vo.modifyUwCondition.ModifyUwConditionResVO outputData) {
        this.outputData = outputData;
    }
}



