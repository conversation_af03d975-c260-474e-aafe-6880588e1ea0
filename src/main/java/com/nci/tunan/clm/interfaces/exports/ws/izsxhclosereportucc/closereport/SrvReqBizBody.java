package com.nci.tunan.clm.interfaces.exports.ws.izsxhclosereportucc.closereport;

import com.nci.tunan.clm.interfaces.vo.zsxhclosereport.CloseReportReqVO;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "InputData")
    protected com.nci.tunan.clm.interfaces.vo.zsxhclosereport.CloseReportReqVO inputData;

    public CloseReportReqVO getInputData() {
        return inputData;
    }

    public void setInputData(CloseReportReqVO inputData) {
        this.inputData = inputData;
    }
}


