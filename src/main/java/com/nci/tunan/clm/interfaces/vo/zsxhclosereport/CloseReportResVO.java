package com.nci.tunan.clm.interfaces.vo.zsxhclosereport;

import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

public class CloseReportResVO implements Serializable {
    /**
     * serialVersionUID 
     */
    private final static long serialVersionUID = 1L;

    /** 返回结果码值 0-成功 1-失败 */
    @XmlElement(name = "resultCode")
    private String resultCode;
    
    /** 返回结果信息 */
    @XmlElement(name = "resultMsg")
    private String resultMsg;

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }
}
