package com.nci.tunan.clm.interfaces.exports.hs.imodifyUwConditionucc.modifyUwCondition;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private final static  long serialVersionUID = 1L;
    
    @XmlElement(name = "InputData")
    protected com.nci.tunan.clm.interfaces.vo.modifyUwCondition.ModifyUwConditionReqVO inputData;
    
    public com.nci.tunan.clm.interfaces.vo.modifyUwCondition.ModifyUwConditionReqVO getInputData() {
        return inputData;
    }
    public void setInputData(com.nci.tunan.clm.interfaces.vo.modifyUwCondition.ModifyUwConditionReqVO inputData) {
        this.inputData = inputData;
    }
}


