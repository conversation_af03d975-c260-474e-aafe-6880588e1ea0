package com.nci.tunan.cap.impl.peripheral.service.r00101002566.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.nci.tunan.cap.dao.ICashDetailDao;
import com.nci.tunan.cap.impl.peripheral.service.r00101002566.IWuXiMedicalInsuranceFeeInfoService;
import com.nci.tunan.cap.interfaces.model.po.CashDetailPO;
import com.nci.tunan.cap.interfaces.peripheral.exports.r00101002566.vo.Input;
import com.nci.tunan.cap.interfaces.peripheral.exports.r00101002566.vo.Output;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
/**
 * <AUTHOR>
 * @.belongToModule 收付费—实收实付查询
 * @date 2017年7月27日下午2:42:05 
 * @description 无锡医保交费信息查询 接口使用 Service层的定义
 */
public class WuXiMedicalInsuranceFeeInfoServiceImpl implements IWuXiMedicalInsuranceFeeInfoService{
	/**
	 * 实收实付Dao层的定义
	 */
	private ICashDetailDao cashDetailDao;
	/**
	 * @description 根据投保单号或保单号查询无锡医保交费信息
	 * @param @param input 投保单号或保单号入参
	 * @param @return 参数
	 */
	@Override
	public Output queryWuXiInfo(Input input) {
		
		SysHeader sysHeader = CommonHeaderDeal.getSYSHEADERTHREAD();
		
		Output  output = new Output();
		com.nci.tunan.cap.interfaces.peripheral.exports.r00101002566.vo.List list  = new com.nci.tunan.cap.interfaces.peripheral.exports.r00101002566.vo.List();
		String contNo = input.getContNo();
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-dd-mm");
		CashDetailPO cashDetailPO = new CashDetailPO();
		cashDetailPO.setBusinessCode(contNo);
		List<CashDetailPO> cashDetailPOs = cashDetailDao.queryWuXiCashDetailInfo(cashDetailPO);
		if(cashDetailPOs == null || cashDetailPOs.isEmpty()){
			sysHeader.setBizResCd("1");
    		sysHeader.setBizResText("无交费信息");
			return output;
		}
		
		cashDetailPO = cashDetailPOs.get(0);
		list.setInsureName(cashDetailPO.getString("insurename")==null?"":cashDetailPO.getString("insurename"));
		list.setInsureIdNo(cashDetailPO.getString("insureidno")==null?"":cashDetailPO.getString("insureidno"));
		String chargeStatus =  cashDetailPO.getBigDecimal("")==null?"":cashDetailPO.getBigDecimal("").toString();
		chargeStatus="0";
		list.setChargeStatus(chargeStatus);
		list.setChargeerrormsg("");
		list.setProcessingDate(cashDetailPO.get("processingdate")==null?"":sdf.format((Date)cashDetailPO.get("processingdate")));
		list.setProcessingCategory(cashDetailPO.getString("processingcategory")==null?"":cashDetailPO.getString("processingcategory"));
		list.setPremiumAmount(cashDetailPO.getBigDecimal("premiumamount")==null?"":cashDetailPO.getBigDecimal("premiumamount").toString());
		list.setSerialNumber(cashDetailPO.getString("serialnumber")==null?"":cashDetailPO.getString("serialnumber"));
		list.setResultCode("0");
		list.setResultMsg("");
		
		output.setList(list);
		sysHeader.setBizResCd("0");
		sysHeader.setBizResText("查询成功");
		
		return output;
	}

	public ICashDetailDao getCashDetailDao() {
		return cashDetailDao;
	}

	public void setCashDetailDao(ICashDetailDao cashDetailDao) {
		this.cashDetailDao = cashDetailDao;
	}
	
	
	

}
