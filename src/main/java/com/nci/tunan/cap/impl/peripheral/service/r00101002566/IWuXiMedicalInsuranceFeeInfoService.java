package com.nci.tunan.cap.impl.peripheral.service.r00101002566;

import com.nci.tunan.cap.interfaces.peripheral.exports.r00101002566.vo.Input;
import com.nci.tunan.cap.interfaces.peripheral.exports.r00101002566.vo.Output;
/**
 * <AUTHOR>
 * @.belongToModule 收付费—实收实付查询
 * @date 2017年7月27日下午2:42:05 
 * @description 无锡医保交费信息查询 接口使用 Service层的定义
 */
public interface IWuXiMedicalInsuranceFeeInfoService {

	/**
	 * @description 根据投保单号或保单号查询无锡医保交费信息
	 * @param @param input 投保单号或保单号入参
	 * @param @return 参数
	 */
	public Output queryWuXiInfo(Input input);
}
