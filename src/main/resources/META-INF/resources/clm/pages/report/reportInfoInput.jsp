﻿﻿﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript" src="clm/pages/report/claimReportInfoInput.js"> </script>

<style type="text/css">       
  	.short{width:70px!important} 
  	.lang{width:350px!important} 
</style>

<script type="text/javascript">
 	dwz_comboxDD_myarray = new Array('provinceReportId','cityReportId','areaReportId');
 	dwz_comboxDD_mybox = navTab.getCurrentPanel();
 	//=====报案方式======输入code值显示对应的name值
 	$("#reportModeId", navTab.getCurrentPanel()).bind("change",function(){
	var optionVal= $(this).find("option[selected=selected]").val().trim();
	$("#reportModeIdText", navTab.getCurrentPanel()).val(optionVal);
    });
 	$("#reportModeIdText", navTab.getCurrentPanel()).keyup(function(){
 		var inputVal= $(this).val();
 		var a=0;
 		$("#reportModeId", navTab.getCurrentPanel()).find("option").each(function(){
 			var optionVal = $(this).val();
 			if(optionVal == inputVal){
 				$(this).attr("selected","selected");
 				a++;
 			}
 		});
 		if(a==0){
 			$("#reportModeId", navTab.getCurrentPanel()).val("1");
 		}
 	});
 	
 	//=====省/直辖市======输入code值显示对应的name值
 	$("#provinceReportId", navTab.getCurrentPanel()).bind("change",function(){
	var optionVal= $(this).find("option[selected=selected]").val().trim();
	$("#accProvinceCodeText", navTab.getCurrentPanel()).val(optionVal);
    });
 	$("#accProvinceCodeText", navTab.getCurrentPanel()).keyup(function(){
 		var inputVal= $(this).val();
 		var a=0;
 		$("#provinceReportId", navTab.getCurrentPanel()).find("option").each(function(){
 			var optionVal = $(this).val();
 			if(optionVal == inputVal){
 				$(this).attr("selected","selected");
 				a++;
 			}
 		});
 		if(a==0){
 			$("#provinceReportId", navTab.getCurrentPanel()).val("110000");//北京市
 		}
 	});
 	
 	//=====市======输入code值显示对应的name值
 	$("#cityReportId", navTab.getCurrentPanel()).bind("change",function(){
	var optionVal= $(this).find("option[selected=selected]").val().trim();
	$("#accCityCodeText", navTab.getCurrentPanel()).val(optionVal);
    });
 	$("#accCityCodeText", navTab.getCurrentPanel()).keyup(function(){
 		var inputVal= $(this).val();
 		var a=0;
 		$("#cityReportId", navTab.getCurrentPanel()).find("option").each(function(){
 			var optionVal = $(this).val();
 			if(optionVal == inputVal){
 				$(this).attr("selected","selected");
 				a++;
 			}
 		});
 		if(a==0){
 			$("#cityReportId", navTab.getCurrentPanel()).val("110100");//市辖区
 		}
 	});
 	
 	//===== 区/县======输入code值显示对应的name值
 	$("#areaReportId", navTab.getCurrentPanel()).bind("change",function(){
	var optionVal= $(this).find("option[selected=selected]").val().trim();
	$("#accDistreactCodeText", navTab.getCurrentPanel()).val(optionVal);
    });
 	$("#accDistreactCodeText", navTab.getCurrentPanel()).keyup(function(){
 		var inputVal= $(this).val();
 		var a=0;
 		$("#areaReportId", navTab.getCurrentPanel()).find("option").each(function(){
 			var optionVal = $(this).val();
 			if(optionVal == inputVal){
 				$(this).attr("selected","selected");
 				a++;
 			}
 		});
 		if(a==0){
 			$("#areaReportId", navTab.getCurrentPanel()).val("110101");//东城区
 		}
 	});
 	  
    if("${tabFlag}"!=''&&"${tabFlag}"!=lastNum-1&&lastNum!=0){
 		ReadOnly($("#reportInfoInput", navTab.getCurrentPanel()));
 		setTimeout('disabledReportInput()',50);
 	}  
    function disabledReportInput(){
    	$("#tabdivclassbrAccId", navTab.getCurrentPanel()).children("div").children("div").children("button").attr("disabled",true);
    	$("#tabdivclassbrClaimTypeId", navTab.getCurrentPanel()).children("div").children("div").children("button").attr("disabled",true);
    }
    function hospitalSignHtmlChange(){
    	var hospitalSignHtml = $("#hospitalSignHtml",navTab.getCurrentPanel()).val();
    	if(hospitalSignHtml==1){
    		$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).html("是");
    		$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).css("color","#000000");
    	}else if(hospitalSignHtml==0){
    		$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).html("否");
    		$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).css("color","#FF0000");
    	}else{
    		$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).html("");
    		$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).css("color","#000000");
    	}
    }
</script>
<%
   String doubleCase =(String)request.getAttribute("doubleCase");
%>
<div id="reportInfoInput" class="pageContent"> 
		<form
			action="clm/report/saveReportInfo_CLM_insertReportInfoInputAction.action?menuId=${menuId}"
			method="post" id="form2"
			name="form2"
			onsubmit="return  validateCallback(this,myNavTabAjaxDone)">
<a lookupGroup="accidentVO" id="accidentVOResultPageId" width="800" height="400"  href="" class="btnLook" style="visibility: hidden;">查询出险结果</a>
<a lookupGroup="laTypeVO" id="seriousDiseasePageId" width="800" height="400"  href="" class="btnLook" style="visibility: hidden;">查询重大疾病</a>
			<div class="panelPageFormContent">
					<input id="statusId" type="hidden"/>
				<dl>
					<dt style="width: 106px">赔 案 号</dt> 
					<dd>
<%-- 					<s:if test="PEflag == 1"> --%>
<%-- 						<input name="claimCaseVO.caseNo" value="${claimCaseVO.caseNo}" id="caseNoId" maxlength="11"/>  --%>
<%-- 					</s:if> --%>
<%-- 					<s:else> --%>
						<input name="claimCaseVO.caseNo" value="${claimCaseVO.caseNo}" id="caseNoId" readonly="true" /> 
<%-- 					</s:else>		 --%>
					<input name="claimCaseVO.caseId" value="${claimCaseVO.caseId}" id="caseIdId" type="hidden" />
					<input id="caseId" type="hidden" value="${claimCaseVO.caseId}">
					</dd>	
				</dl>
				<dl>
					
					<dt>事 件 号</dt>
					<dd> <input name="claimAccidentVO.accidentNo"
						value="${claimAccidentVO.accidentNo}" id="accidentNoId"
						readonly="true" /> <input name="claimAccidentVO.accidentId"
						value="${claimAccidentVO.accidentId}" id="accidentIdReportId" type="hidden" /><input type="hidden" id="flagId" name="claimAccidentVO.flag" disabled="disabled"/>
					</dd>
				</dl>
			</div>
			<div class="panelPageFormContent" >
				 

						<dl >
							<dt >
								<font >* </font>报案人与出险人关系 
							</dt>
							<dd >
 								<Field:codeTable  id="rptrRelationId"   whereClause=" 1=1"  orderBy=" decode(relation_code,'01','1','06','2','07','3','08','4','09','5','10','6','05','7','04','8','03','9','02','99','99','999',relation_code) "  cssClass="notuseflagDelete combox title"    
 									name="claimCaseVO.rptrRelation" 
 									value="${claimCaseVO.rptrRelation}" tableName="APP___CLM__DBUSER.T_RPTR_RELATION" onChange="changClaimRelation(this);"></Field:codeTable> 
							</dd>
						</dl>

						<dl >
							<dt >
								<font >* </font>报案人姓名 
							</dt>
							<dd >
								<input name="claimCaseVO.rptrName" id="rptrNameId"
									value="${claimCaseVO.rptrName}" onblur="checkName(this)" />
							</dd>
						</dl>

						<dl >
							<dt >
								<font class="point" color="red">* </font>报案人电话 
							</dt>
							<dd >
								<input name="claimCaseVO.rptrMp" id="rptrTelId"  auto="true" 
									value="${claimCaseVO.rptrMp}" class="" >
<!-- 								<input name="claimCaseVO.rptrMp" id="rptrTelId" -->
<%-- 									value="${claimCaseVO.rptrMp}" class=""  name="mobile"   onkeyup="changePhone(this)" > --%>
							</dd>
						</dl>

						<dl >
							<dt >报案人邮编</dt>
							<dd >
								<input type="expandPostCode" name="claimCaseVO.rptrZip" value="${claimCaseVO.rptrZip}" onkeyup="this.value=this.value.replace(/\D/g,'')" 
								  id="rptrZipId" maxlength="6"/>
							</dd>
						</dl>
						<dl >
							<dt >报案人地址</dt>
							<dd >
								<input type="text" size="93" name="claimCaseVO.rptrAddr"
									value="${claimCaseVO.rptrAddr}" id="rptrAddrId">
							</dd>
						</dl>
						<dl >
							<dt >报案人邮箱</dt>
							<dd >
								<input name="claimCaseVO.rptrEmail"
									value="${claimCaseVO.rptrEmail}" id="rptrEmailId" class="email" />
							</dd>
						</dl>
						<dl >
							<dt >
								<font >* </font>报案方式 
							</dt>
							<dd > 
								<Field:codeTable id="reportModeId" name="claimCaseVO.reportMode" cssClass="notuseflagDelete combox title"
									value="${claimCaseVO.reportMode}" tableName="APP___CLM__DBUSER.T_REPORT_TYPE" orderBy="code" onChange="changePayMode();"
									whereClause="code in (1,2,3,4,5,6,7,8,9,10,12,13)"></Field:codeTable>
							        <input type="hidden" value="1" id="reportModeIdText" value="${claimCaseVO.reportMode}" size="5" title="请输入码值查询"/>
									<input type="hidden" name="claimCaseVO.greenFlag" value="${claimCaseVO.greenFlag}"/>
							</dd>
						</dl>
					<s:if test="claimCaseVO.reportMode==1">
												<dl  id="AreportSourceId">
							<dt >
								报案渠道来源
							</dt>
							<dd > 
							
									<input value='<Field:codeValue tableName="APP___CLM__DBUSER.t_Report_Source" value="${claimCaseVO.reportSource}"/>' id="reportSourceID" name="claimCaseVO.reportSourceName" readonly="true" /> 
									<input type="hidden" value="${claimCaseVO.reportSource}"  id="reportSource" name="claimCaseVO.reportSource"  /> 
							</dd>
						</dl>
					</s:if>
					<s:else>
						<dl style="display:none;" id="AreportSourceId">
							<dt >
								报案渠道来源
							</dt>
							<dd > 
								<input value='<Field:codeValue tableName="APP___CLM__DBUSER.t_Report_Source" value="${claimCaseVO.reportSource}"/>' id="reportSourceID" name="claimCaseVO.reportSourceName" readonly="true" /> 
								<input type="hidden" value="${claimCaseVO.reportSource}"  id="reportSource" name="claimCaseVO.reportSource"  /> 
							</dd>
						</dl>
					
					</s:else>
							<s:if test="claimCaseVO.reportMode==1">
						<dl id="AisCalledBackId">
							<dt >
								是否已回呼
							</dt>
							<dd > 

								<input name="claimCaseVO.isCalledBackName" value='<Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimCaseVO.isCalledBack}"/>' id="isCalledBackId"    readonly="true" >
								<input type="hidden" value="${claimCaseVO.isCalledBack}"  id="isCalledBack" name="claimCaseVO.isCalledBack"  /> 
				
							</dd>
						</dl>
					</s:if>
					<s:else>
					<dl style="display:none;" id="AisCalledBackId">
							<dt >
								是否已回呼
							</dt>
							<dd > 
								<input name="claimCaseVO.isCalledBackName" value='<Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimCaseVO.isCalledBack}"/>' id="isCalledBackId"    readonly="true" >
								<input type="hidden" value="${claimCaseVO.isCalledBack}"  id="isCalledBack" name="claimCaseVO.isCalledBack"  /> 
							</dd>
						</dl>
					
					</s:else>
					
						<dl >
							<dt >
								<font >* </font>申请类型 
							</dt>
							<dd >
							 
								<Field:codeTable  id="applyTypeId" cssClass="notuseflagDelete combox title"
									name="claimCaseVO.caseApplyType"
									value="${claimCaseVO.caseApplyType}" tableName="APP___CLM__DBUSER.T_APPLY_TYPE"></Field:codeTable>
							</dd>
						</dl>


						<dl >
							<dt >报案日期</dt>
							<dd >
								<div class="main_datawhite">
								<s:if test="caseId ==null || caseId == -1">
									<s:date name="claimCaseVO.acceptTime" format='yyyy-MM-dd' />
									<input
									id="systemTimeId" name="claimCaseVO.acceptTime" readonly="readonly"
									value="<s:date name="claimCaseVO.acceptTime" format='yyyy-MM-dd HH:mm:ss' />" type="hidden" />
								</s:if>
								<s:else>
									<s:date name="claimCaseVO.firstRptrTime" format='yyyy-MM-dd' />
									<input
									id="systemTimeId" name="claimCaseVO.acceptTime" readonly="readonly"
									value="<s:date name="claimCaseVO.firstRptrTime" format='yyyy-MM-dd HH:mm:ss' />" type="hidden" />
								</s:else>
								</div>
						
							</dd>
						</dl>
						<dl >
						<dt >管理机构</dt>
							<dd >
							<div class="main_datawhite">${claimCaseVO.signOrgan}</div>
								<input id="" type="hidden" name="claimCaseVO.signOrgan" value="${claimCaseVO.signOrgan}" size="2" readOnly />
							</dd>
						</dl>
						<dl >
							<dt >报案受理人</dt>
							<dd >
								<div class="main_datawhite">${claimCaseVO.userRealName}</div> <input
									name="claimCaseVO.rptrId" value="${claimCaseVO.rptrId}" id="" type="hidden">
							</dd>
						</dl>
			</div>

			<div id="accReasonButton" >
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">出险信息
					</h1>
					</div>
				<div>

					<div class="panelPageFormContent" >

						<dl >
							<dt >出险人姓名</dt>
							<dd >
								<div class="main_datawhite">${claimCaseVO.insured.customerName}</div>
								<input name="claimCaseVO.insured.customerName" value="${claimCaseVO.insured.customerName}"
									id="customerName" readonly="true" type="hidden"> <input
									name="claimCaseVO.insuredId" value="${claimCaseVO.insuredId}" type="hidden" id="insuredId"/>
							</dd>
						</dl>
						<dl >
							<dt >证件号码</dt>
							<dd >
								<div class="main_datawhite">${claimCaseVO.insured.customerCertiCode}</div>
								<input name="claimCaseVO.insured.customerCertiCode"
									value="${claimCaseVO.insured.customerCertiCode}" id="" readonly="true" type="hidden"/> 
 									<input
									name="claimCaseVO.insured.birthDate"
									value="${claimCaseVO.insured.birthDate}" id="accBirthId"
									type="hidden" />
							</dd>
						</dl>

						<dl >
							<dt >性别</dt>
							<dd >
								<s:if test="claimCaseVO.insured.customerGender==1">
									<input name="claimCaseVO.insured.customerGender" value="1" id="customerGenderId" type="hidden" />
									<input value="男" readonly="true" type="hidden"/>
									<div class="main_datawhite">男</div>
								</s:if>
								<s:if test="claimCaseVO.insured.customerGender==2">
									<input name="claimCaseVO.insured.customerGender" value="2" id="customerGenderId" type="hidden">
									<input value="女" readonly="true" type="hidden"/>
									<div class="main_datawhite">女</div>
								</s:if>
								<s:if test="claimCaseVO.insured.customerGender==9">
									<input name="claimCaseVO.insured.customerGender" value="9" id="" type="hidden">
									<input value="未知" readonly="true" type="hidden"/>
									<div class="main_datawhite">未知</div>
								</s:if>
							</dd>
						</dl>
						<dl >
							<dt >
								<font >* </font>事故日期 
							</dt>
							<dd >

								<input id="accidentTimeId" type="expandDateYMD" class="date"
									name="claimAccidentVO.accDate" value="<s:date name='claimAccidentVO.accDate' format='yyyy-MM-dd'/>" postField="keyword"
									suggestFields="accDate"
									suggestUrl="clm/pages/report/queryExistedEvent.jsp"
									lookupGroup="claimAccidentVO" onPropertychange="updateAccidentNo(this);"> <a class="btnLook"
									href="clm/report/queryExistedEventInit_CLM_insertReportInfoInputAction.action"
									lookupGroup="claimAccidentVO" onclick="myReportOption(this);">查询已有事件</a> <input id="systemTimeId"
									name="claimCaseVO.systemDateString" value="" type="hidden" />
									<!-- 用于已有事故返回接值 -->
									<input type="hidden" id="accDateReporId" value="<s:date name='claimAccidentVO.accDate' format='yyyy-MM-dd'/>">
									<input name="claimAccidentVO.accidentDetail" id="accidentDetailReportId" type="hidden">
									<input name="claimAccidentVO.accidentDetailName" id="accidentDetailNameReportId" type="hidden">
									<input name="claimAccidentVO.cureHospital" id="cureHospitalReportId" type="hidden">
									<input name="claimAccidentVO.cureHospitalName" id="cureHospitalNameReportId" type="hidden">
									<input name="claimAccidentVO.cureStatus" id="cureStatusReportId" type="hidden">
									<input name="claimAccidentVO.seriousDisease" id="seriousDiseaseReportId" type="hidden">
							</dd>
						</dl>
						<dl >
							<dt >治疗情况</dt>
							<dd >
							 
								<Field:codeTable  id="cureStatusId" name="claimCaseVO.cureStatus" cssClass="notuseflagDelete combox title"
									value="${claimCaseVO.cureStatus}" tableName="APP___CLM__DBUSER.T_CURE_STATUS" nullOption="true"/>
						</dl>
						<dl >
							<dt >治疗医院</dt>
							<dd >
								<input id="inputHospital" name="claimCaseVO.cureHospital" value="${claimCaseVO.cureHospital}" size="10" type="hidden" />
								<input  id="hospitalSignHtml" name="claimCaseVO.hospitalSignHtml" value="${hospitalServiceVO.hospitalSignHtml}" type="hidden"
								 onpropertychange="hospitalSignHtmlChange()"/>
								<input  id="hospitalNameReportId" name="claimCaseVO.hospitalName" value="${claimHospitalServiceVO.hospitalName}" type="text"
									postField="keyword" suggestFields="hospitalName,hospitalCode" readonly
									suggestUrl="clm/pages/report/bringBackHospital.jsp"
									lookupGroup="claimCaseVO" /> <a class="btnLook"
									href="clm/report/hospitalInfoQuery_CLM_hospitalInfoQueryAction.action?leftFlag=0&menuId=${menuId}"
									lookupGroup="claimCaseVO" width="900" height="450">查询治疗医院</a>

							</dd>
						</dl>
						<dl >
							<dt ><font >* </font>出险原因 </dt>
							<dd >
								<Field:codeTable  id="accReasonId"
										name="claimAccidentVO.accReason" cssClass="notuseflagDelete combox title"
										value="${claimAccidentVO.accReason}"
										tableName="APP___CLM__DBUSER.T_CLAIM_NATURE" whereClause="code in (1,2)" onChange="accidentReason();" orderBy="code"/>
							</dd>
						</dl>
						<dl >
							<dt >意外细节</dt>
							<dd >
								<input id="findaccidentDetails"
									name="claimCaseVO.accidentDetail"
									value="${claimCaseVO.accidentDetail}" type="hidden" /> <input
									id="detailDescId" name="claimCaseVO.DetailDesc" value="${accidentDetailVO.detailDesc}" type="text"
									postField="keyword" suggestFields="accDetailCode,accDetailDesc" readonly
									suggestUrl="clm/pages/report/findAccidentDetails.jsp"
									lookupGroup="claimCaseVO" /> <a class="btnLook"
									href="clm/report/queryPage_CLM_accidentDetailsAction.action?leftFlag=0&menuId=${menuId}"
									lookupGroup="claimCaseVO" id="btnLookAccidnetDetalId">查询意外细节</a>
							</dd>
						</dl>
						<dl >
							<dt >轻度疾病</dt>
							<dd > 
								<input id="findSeriousDiseaseId1" name="claimCaseVO.ligName" value="${laTypeVO.ligName}"  
									postField="keyword" suggestFields="code1,name1" 
									suggestUrl="clm/pages/report/findSeriousDisease.jsp" readonly
									lookupGroup="claimCaseVO" /><a class="btnLook"
									href="clm/report/querySeriousDisease_CLM_insertReportInfoInputAction.action?leftFlag=0&menuId=${menuId}&btnLookSeriousDiseaseId=1"
									lookupGroup="claimCaseVO" id="btnLookSeriousDiseaseId1" >查询轻度疾病</a>
								<input id="seriousDiseaseId1" name="claimCaseVO.seriousDisease1" value="${claimCaseVO.seriousDisease1}" type="hidden"/>
							</dd>
						</dl>
						<dl >
							<dt >中度疾病</dt>
							<dd > 
								<input id="findSeriousDiseaseId2" name="claimCaseVO.midName" value="${laTypeVO.midName}"  
									postField="keyword" suggestFields="code2,name2" 
									suggestUrl="clm/pages/report/findSeriousDisease.jsp" readonly
									lookupGroup="claimCaseVO" /><a class="btnLook"
									href="clm/report/querySeriousDisease_CLM_insertReportInfoInputAction.action?leftFlag=0&menuId=${menuId}&btnLookSeriousDiseaseId=2"
									lookupGroup="claimCaseVO" id="btnLookSeriousDiseaseId2">查询中度疾病</a>
								<input id="seriousDiseaseId2" name="claimCaseVO.seriousDisease2" value="${claimCaseVO.seriousDisease2}" type="hidden"/>
							</dd>
						</dl>
						<dl >
							<dt >重大疾病</dt>
							<dd > 
								<input id="findSeriousDiseaseId" name="claimCaseVO.name" value="${laTypeVO.name}"  
									postField="keyword" suggestFields="code,name" 
									suggestUrl="clm/pages/report/findSeriousDisease.jsp" readonly
									lookupGroup="claimCaseVO" /><a class="btnLook"
									href="clm/report/querySeriousDisease_CLM_insertReportInfoInputAction.action?leftFlag=0&menuId=${menuId}&btnLookSeriousDiseaseId=3"
									lookupGroup="claimCaseVO" id="btnLookSeriousDiseaseId">查询重大疾病</a>
								<input id="seriousDiseaseId" name="claimCaseVO.seriousDisease" value="${claimCaseVO.seriousDisease}" type="hidden"/>
							</dd>
						</dl>
						<div class="mian_site">
							<dl>
								<dt><font>* </font>出险地点</dt>
							</dl>
							<div class="main_detail">
								<input type="hidden" name="claimAccidentVO.accProvince1" value="${claimAccidentVO.accProvince}" id="accProvince1ReportId">
                             	<input type="hidden" name="claimAccidentVO.accCity1" value="${claimAccidentVO.accCity}" id="accCity1ReportId">
                             	<input type="hidden" name="claimAccidentVO.accDistreact1" value="${claimAccidentVO.accDistreact}" id="accDistreact1ReportId">
                             	<input type="hidden" name="claimAccidentVO.accCountryCode1" value="${claimAccidentVO.accCountryCode}" id="accCountryCode1ReportId">
                             	<input type="hidden" name="claimAccidentVO.accDistreactName1" value="${claimAccidentVO.accDistreactName}" id="accDistreactName1ReportId">
								<input type="hidden" name="claimAccidentVO.accProvincefz" value="${claimAccidentVO.accProvince}" id="accProvince1ReportIdfz">
								<input type="hidden" name="claimAccidentVO.accCityfz" value="${claimAccidentVO.accCity}" id="accCity1ReportIdfz">
								<input type="hidden" name="claimAccidentVO.accDistreactfz" value="${claimAccidentVO.accDistreact}" id="accDistreact1ReportIdfz">
								<dl><dd><Field:codeTable cssClass="selectToInput"  onChange="contryReportFun(this);" value="${claimAccidentVO.accCountryCode}" nullOption="true" name="claimAccidentVO.accCountryCode" tableName="APP___CLM__DBUSER.T_COUNTRY" id="contryReportId" defaultValue="CHN"/><span>-</span></dd></dl>
								<dl><dd><Field:codeTable cssClass="selectToInput"  name="claimAccidentVO.accProvince" onChange="ProvinceChangeReport(this);" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="provinceReportId" whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/><span>-</span></dd></dl>
								<dl><dd><select class="selectToInput"     onchange="cityChageReport(this);" id='cityReportId' name="claimAccidentVO.accCity"  style="width:150px;"></select><span>-</span></dd></dl>
								<dl><dd><select class="selectToInput"  onchange="DistreactChageReport(this);"  id="areaReportId" name="claimAccidentVO.accDistreact"  style="width:120px;">
									</select><span>-</span></dd></dl>
								<dl><dd>
								<s:if test="claimAccidentVO.accStreet != null || claimAccidentVO.accStreet != ''">
                             			<input name="claimAccidentVO.accStreet" id="accStreetReportId" size="24" value="${claimAccidentVO.accStreet}" style="width: 200px;">
                             		</s:if>
                             		<s:else>
	                             		<input name="claimAccidentVO.accStreet" id="accStreetReportId" size="24" value="乡镇/街道${claimAccidentVO.accStreet}" style="width: 200px;">
                             		</s:else></dd></dl>
							</div>
						</div>
<!-- 						<dl style="height: auto;"> -->
<!-- 							<dt >出险地点 </dt> -->
<!-- 							<dd> -->
<%-- 								<input type="hidden" name="claimAccidentVO.accProvince1" value="${claimAccidentVO.accProvince}" id="accProvince1ReportId"> --%>
<%--                              	<input type="hidden" name="claimAccidentVO.accCity1" value="${claimAccidentVO.accCity}" id="accCity1ReportId"> --%>
<%--                              	<input type="hidden" name="claimAccidentVO.accDistreact1" value="${claimAccidentVO.accDistreact}" id="accDistreact1ReportId"> --%>
<%--                              	<input type="hidden" name="claimAccidentVO.accCountryCode1" value="${claimAccidentVO.accCountryCode}" id="accCountryCode1ReportId"> --%>
<%--                              	<input type="hidden" name="claimAccidentVO.accDistreactName1" value="${claimAccidentVO.accDistreactName}" id="accDistreactName1ReportId"> --%>
<%--                              	<Field:codeTable cssClass="selectToInput"  onChange="contryReportFun(this);" nullOption="true" value="${claimAccidentVO.accCountryCode}" name="claimAccidentVO.accCountryCode" tableName="APP___CLM__DBUSER.T_COUNTRY" id="contryReportId" defaultValue="CHN"/> --%>
<!--                              	<font>* </font>国家 -->
<!-- 							</dd> -->
<!-- 							<dd style="width: 230px;padding-left: 20px;"> -->
<%-- 								<Field:codeTable cssClass="selectToInput"  name="claimAccidentVO.accProvince" onChange="ProvinceChangeReport(this);" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="provinceReportId" whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/> --%>
<!-- 								<font>* </font>省/直辖市 -->
<!-- 							</dd> -->
<!-- 							<dd style="padding-left: 25px;"> -->
<%-- 			                    <select class="selectToInput"     onchange="cityChageReport(this);" id='cityReportId' name="claimAccidentVO.accCity"  style="width:150px;"></select> --%>
<!-- 								<font >* </font>市 -->
<!-- 							</dd> -->
<!-- 							<dd> -->
<%-- 								<select class="selectToInput"    id="areaReportId" name="claimAccidentVO.accDistreact"  style="width:120px;"> --%>
<%-- 									</select> --%>
<!-- 									<font >* </font>区/县 -->
<!-- 							</dd> -->
<!-- 						</dl> -->
<!-- 						<dl > -->
<!-- 							<dt>&nbsp;</dt> -->
<%-- 							<dd style="width: 210px;"><input name="claimAccidentVO.accStreet" id="accStreetReportId" size="24" value="${claimAccidentVO.accStreet}" />乡镇/街道</dd> --%>
<!-- 						</dl>	 -->
							<dl>
								<dt>认可医院标识</dt>
								<dd>
									<s:if test='claimHospitalServiceVO.isDesignated == 0'>
										<div id="hospitalSignHtmlInput" class="main_datawhite" style="color: #FF0000">否</div>
									</s:if>
									<s:elseif test='claimHospitalServiceVO.isDesignated == 1'>
										<div id="hospitalSignHtmlInput" class="main_datawhite">是</div>
									</s:elseif>
									<s:else>
										<div id="hospitalSignHtmlInput" class="main_datawhite"></div>
									</s:else>
								</dd>
							</dl>	
							<dl >
							<dt style="width: 155px;">社保状态</dt>
							<dd>
								<s:if test='sociSecu == "0"'>
									<div id="sociSecuDiv" class="main_datawhite">否</div>
								</s:if>
								<s:elseif test='sociSecu == "1"'>
									<div id="sociSecuDiv" class="main_datawhite">是</div>
								</s:elseif>
								<s:else>
									<div id="sociSecuDiv" class="main_datawhite"></div>
								</s:else>
							</dd>							 
						</dl>
						<dl>
							<dt style="width: 155px;">确诊时间</dt>
							<dd>
								<input type="expandDateYMD" id="diagnosisTime" name="claimCaseVO.diagnosisTime" class="date" value="<s:date name='claimCaseVO.diagnosisTime' format='yyyy-MM-dd'/>"  /><a class="inputDateButton" href="javascript:;">选择</a>
							</dd>							 
						</dl>
<!-- 						<dl > -->
<!-- 							<dt >出险地点</dt> -->
<!-- 							<dd > -->
<%--                              	<input type="hidden" name="claimAccidentVO.accProvince1" value="${claimAccidentVO.accProvince}" id="accProvince1ReportId"> --%>
<%--                              	<input type="hidden" name="claimAccidentVO.accCity1" value="${claimAccidentVO.accCity}" id="accCity1ReportId"> --%>
<%--                              	<input type="hidden" name="claimAccidentVO.accDistreact1" value="${claimAccidentVO.accDistreact}" id="accDistreact1ReportId"> --%>
<%--                              	<input type="hidden" name="claimAccidentVO.accCountryCode1" value="${claimAccidentVO.accCountryCode}" id="accCountryCode1ReportId"> --%>
<%--                              	<input type="hidden" name="claimAccidentVO.accDistreactName1" value="${claimAccidentVO.accDistreactName}" id="accDistreactName1ReportId"> --%>
<!--                              </dd>	 -->
<!--                              	<div style="float: left;margin-right: 3%;" style="width: 70%"> -->
<%--                              		<Field:codeTable cssClass="selectToInput"  onChange="contryReportFun(this);" value="${claimAccidentVO.accCountryCode}" name="claimAccidentVO.accCountryCode" tableName="APP___CLM__DBUSER.T_COUNTRY" id="contryReportId" defaultValue="CHN"/> --%>
<!--                              		国家<font class="point" color="red">*</font>&nbsp; -->
<!-- 								</div> -->
								
<!-- 								<div style="float: left;margin-right: 7%;" style="width: 70%"> -->
<%--                              		<Field:codeTable cssClass="selectToInput"  name="claimAccidentVO.accProvince" onChange="ProvinceChangeReport(this);" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="provinceReportId" whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/> --%>
<!-- 									省/直辖市<font class="point" color="red">*</font>&nbsp; -->
<!-- 								</div> -->
								
<!-- 								<div style="float: left;"> -->
<%-- 									<select class="selectToInput"     onchange="cityChageReport(this);" id='cityReportId' name="claimAccidentVO.accCity"  style="width:150px;"> --%>
<!-- 									<font >* </font>市 -->
<%-- 									</select> --%>
<!-- 								</div>								 -->
<!-- 						</dl> -->
<!-- 						<dl > -->
<!-- 							<dd > -->
<!-- 								<div style="float: left;"> -->
<%-- 									<select class="selectToInput"    id="areaReportId" name="claimAccidentVO.accDistreact"  style="width:120px;"> --%>
<%-- 									</select>									 --%>
<!-- 									区/县 <font class="point" color="red">*</font>&nbsp;&nbsp; -->
<!-- 								</div> -->
<!-- 								<div style="float: left;margin-left: 20px;"> -->
<%-- 									<span> --%>
<%-- 										<input name="claimAccidentVO.accStreet" id="accStreetReportId" size="24" value="${claimAccidentVO.accStreet}" /> --%>
<%-- 									</span> --%>
<%-- 									<span style="float: left">乡镇/街道</span> --%>
<!-- 								</div> -->
<!-- 							</dd> -->
<!-- 						</dl> -->
						
						<dl style="height: auto;width: 100%;">
							<dt>
								<font >* </font>事故描述
							</dt>
							<dd >
								<textarea name="claimAccidentVO.accDesc"
									id="accidentDescId" rows="3" cols="100" maxlength="1000">${claimAccidentVO.accDesc}</textarea>
							</dd>
						</dl>
					</div>
					<div class="panelPageFormContent" >	
						<div class="tabdivclassbr" id="tabdivclassbrAccId">
						<table id="accResult" class="list nowrap itemDetail"
							addButton="添加出险结果" width="100%"   style="margin-bottom: 20px;padding-bottom: 10px;">
							<thead>
								<tr>
								<th type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="10" width="0%" fieldClass="digits">序号</th>
									<th type="enum" name="claimAccidentResultVO.accResult1"
										enumUrl="clm/pages/report/accidentResultOne.jsp?accType="
										size="12">出险结果1</th>
									<th type="enum" name="claimAccidentResultVO.accResult2"
										enumUrl="clm/pages/report/accidentResultTwo.jsp" size="12">出险结果2</th>
									<th type="del" width="60">操作</th>
								</tr>
							</thead>
							<tbody class="tbodys" id="tbodysId">
							<input type="hidden" id="accidentResultBiaoId">
							<input type="hidden" name="accidentVO.accident1Code" id="accident1CodID">
							<input type="hidden" name="accidentVO.accident1Name" id="accident1NamID">
							<input type="hidden" name="accidentVO.accident2Code" id="accident2CodID">
							<input type="hidden" name="accidentVO.accident2Name" id="accident2NamID">
							<s:if test="listResultVo==null||listResultVo.size()==0">
								<tr style="background-color: #fff">
									<td>
										<input name="items[0].itemInt" class="digits textInput focus" type="text" size="10"  value="1" readonly="readonly"/>
									</td>
									<td >
										<!-- 添加俩个无用标签和添加的时候对应上 -->
										 <input type="hidden"/>
										 <input type="hidden"/>
										 <input class="short"  readonly="readonly" name="claimAccidentResultVO.accResult1" id="accResult1" onclick="accidentVOResultPage(this);">
										 <input class="lang"  readonly="readonly" name="claimAccidentResultVO.accResultDesc1" id="accResult1Name" type="text"  onclick="accidentVOResultPage(this);"/> 
									</td>
									<td >	
										<!-- 添加俩个无用标签和添加的时候对应上 -->
										 <input type="hidden"/>
										 <input type="hidden"/>
										<input class="short"  readonly="readonly" name="claimAccidentResultVO.accResult2" id="accResult2" onclick="accidentVOResultPage(this);">
										<input class="lang"  readonly="readonly" name="claimAccidentResultVO.accResultDesc2" type="text" id="accResult2Name" onclick="accidentVOResultPage(this);"/>
									</td>
									<td><a class="btnDel"></a></td>
								</tr>
							</s:if>
							<s:else>
								<s:iterator value="listResultVo" var="rlist" status="st">
									<tr style="background-color: #fff">
										<td>
											<input name="items[${st.index+1}].itemInt" class="digits textInput focus" type="text" size="10"  value="${st.index+1}" readonly="readonly"/>
											<input value="" id='accResult1Changeinforeport' type="hidden">
										<input value="" id='accResult1ChangeIndexreport' type="hidden">
										<input value="" id='accResult1ChangeinfoTworeport' type="hidden">
										<input value="" id='accResult1ChangeIndexTworeport' type="hidden">
										</td>
										<td >
											<!-- 添加俩个无用标签和添加的时候对应上 -->
										 <input type="hidden"/>
										 <input type="hidden"/>
											<input class="short"  readonly="readonly" name="claimAccidentResultVO.accResult1" id="accResult1" onclick="accidentVOResultPage(this);" value="${rlist.accResult1}">
											 <input class="lang"  readonly="readonly" name="claimAccidentResultVO.accResultDesc1" id="accResult1Name" value="${rlist.accResultDesc1}" type="text" onclick="accidentVOResultPage(this);"  readonly="true"/> 
										</td>
										<td >	
											<!-- 添加俩个无用标签和添加的时候对应上 -->
										 <input type="hidden"/>
										 <input type="hidden"/>
											<input class="short"  readonly="readonly" name="claimAccidentResultVO.accResult2" value="${rlist.accResult2}" id="accResult2" onclick="accidentVOResultPage(this);">
											<input class="lang"  readonly="readonly" name="claimAccidentResultVO.accResultDesc2" value="${rlist.accResultDesc2}" type="text" id="accResult2Name" onclick="accidentVOResultPage(this);"/>
										</td>
										<td><a class="btnDel"></a></td>
									</tr>
								</s:iterator>
							</s:else>
						</tbody>
						</table>
			</div>
			<div class="tabdivclassbr" id="tabdivclassbrClaimTypeId">
						<table id="tableId" id="flag"
							class="list nowrap itemDetail" addButton="添加理赔类型" width="100%" >
							<thead>
								<tr>
									<th type="enum" name="claimSubCaseVO.claimType"  
										enumUrl="clm/pages/html/claimType.jsp" size="12" >理赔类型</th>
									<th type="enum" name="claimSubCaseVO.claimDate"
										enumUrl="clm/pages/html/accidentTimereport.html" size="12">出险日期</th>
									<th type="enum" name="claimSubCaseVO.accAgeString"
										enumUrl="clm/pages/html/accidentAge.html" size="12">出险年龄</th>
									<th type="del" width="60">操作</th>
								</tr>
							</thead>
							<tbody id="tbodyIdReport">
								<input type="hidden" id="tclmJson" name="claimSubCaseVO.clmJson" value="" />
								<s:if test="ClaimSubCaseVOs==null||ClaimSubCaseVOs.size()==0">
									<tr style="background-color: #fff">
										<td id="aaa"><Field:codeTable   nullOption='true' cssClass="notuseflagDelete combox title"
 												name="claimSubCaseVO.claimType" id="claimType"
												value="${claimSubCaseVO.claimType}" tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" whereClause="code not in (11)"  orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)"></Field:codeTable>
										</td>
										<td><input id="accTime" type="expandDateYMD"
											name="claimSubCaseVO.claimDate" value="" class="date"
											onPropertychange="countAgeReport(this)"/> <a
											class="inputDateButton" href="javascript:;">选择</a></td>

										<td><input type="text" name="claimSubCaseVO.accAgeString"
											readonly="readonly" value="" /></td>
										<td><a class="btnDel"></a></td>
									</tr>
								</s:if>
								
								<s:else>
									<s:iterator value="ClaimSubCaseVOs" status="sub" var="subCase">
										<tr style="background-color: #fff">
											<td id="aaa"><Field:codeTable  whereClause="code not in (11)" nullOption='true' cssClass="notuseflagDelete combox title"
													name="claimSubCaseVO.claimType"
													value="${claimType}"
													tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" orderBy=" decode(code, '01',1,'04',2,'03',3,'02',4,'08',5,'10',6,'06',7,'07',8)"></Field:codeTable>
											 <td>
											 
												<input id="accTime" type="expandDateYMD" name="claimSubCaseVO.claimDate" value='<fmt:formatDate value="${claimDate}" pattern="yyyy-MM-dd"/>'
													 class="date" onPropertychange="countAgeReport(this)"/>
												<a class="inputDateButton" href="javascript:;">选择</a>
												<input type="hidden" id="hiddenSubCaseId" value="${subCaseId}"/>
											</td> 
											<td><input type="text"
												name="claimSubCaseVO.accAgeString" readonly="readonly"
												value="${claimSubCaseVO.accAgeString}" /></td>
											<td><a class="btnDel"></a></td>
										</tr>
									</s:iterator>
								</s:else>
							</tbody>
						</table>
						</div>
					</div>
				</div>
			</div>
			
			<s:set name="caseId" value="%{caseId}" />
				<s:if test="caseId ==null">
					<s:set name="caseId" value="-1" />
				</s:if>
			<div class="formBarButton main_bottom">
				<ul>
					<li>
						<button class="but_blue" id="prevReportId" type="button" onclick="prev('1','form2','${caseId}')">上一步</button>
					</li>
					<li>
						<button class="but_blue" type="button" onclick="doSaveReport(0)">保存</button>
					</li>
					<li>
						<button id="nextId" class="but_blue" type="button" onclick="doSaveReport(1)">下一步</button>
					</li>
					<li>
						<a id="cheXiaoId" class="but_blue main_buta"
					href="javaScript:void(0)" onclick="cheXiao();" width="1000" height="480" max="true"
					lookupGroup=""> <span>撤销</span></a>
					</li>
					<li>
						<button class="but_gray" type="button" onclick="exit()">退出</button>
					</li>
				</ul>
			</div>
		</form>
</div>
