private boolean extracted(List<String> taskIdList, List<String> ressignUserList, Map<String, String> map) {
		List<String> relativesToAvoidList = new ArrayList<>();
		// 轮流将任务分配给指派人员
		for (int i = ClaimConstant.ZERO, j = ClaimConstant.ZERO; 
			 	i < taskIdList.size() && j < ressignUserList.size(); i++, j++) {
			
			// 获取人员的信息
			UserPO userPO = new UserPO();
			userPO.setUserName(ressignUserList.get(j));
			List<UserPO> userPOList = claimUserDao.findAllUser(userPO);
			
			//分配人员不存在
			if (userPOList.isEmpty()) {
				map.put("isMsg", "true");
				map.put("msg", ressignUserList.get(j) + "  不存在，请重新选择作业人员!");
				return true;
			}

			// 亲属规避规则检查
			String caseNo = taskIdList.get(i).split(ClaimConstant.SPLIT_STR)[ClaimConstant.ONE];
			boolean isRelativeAvoidanceNeeded = checkRelativesAvoidRule(caseNo, null, userPOList.get(ClaimConstant.ZERO).getIdNumber());
			if (isRelativeAvoidanceNeeded) {
				relativesToAvoidList.add(userPOList.get(ClaimConstant.ZERO).getRealName());
			}
			// 指派人员循环到最后一个，但任务还未分配完，重置指派人员索引
			if (ressignUserList.size() -j == ClaimConstant.ONE && i < taskIdList.size() -1) {
				// 设置为-1，重新进行循环作业人员
				j = ClaimConstant.ADJUST;
			}
			
		}

		// 如果有需要规避的亲属，则弹出提示
		if (!CollectionUtils.isEmpty(relativesToAvoidList)) {
			// 去除重复的亲属名称 (JDK 1.7 compatible)
			LinkedHashSet<String> uniqueRelativesToAvoid = new LinkedHashSet<String>(relativesToAvoidList);
			relativesToAvoidList = new ArrayList<String>(uniqueRelativesToAvoid);
			
			String avoidedRelativesNames = StringUtils.join(relativesToAvoidList, "、");
			map.put("isMsg", "true");
			map.put("msg",String.format("当前分配人员%s，涉及案件亲属回避，请更换分配人。", avoidedRelativesNames));
			return true;
		}
		
		return false;
	}